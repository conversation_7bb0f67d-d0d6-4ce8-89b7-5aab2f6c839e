import {
  SidebarInset,
  SidebarProvider,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { InnerSidebar, OuterSidebar } from "./_components";
import {
  ServiceInnerSidebarProvider,
  ServiceSidebarProvider,
} from "./_contexts/sidebar";

export default function ServiceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <ServiceSidebarProvider>
        <OuterSidebar />
        <ServiceInnerSidebarProvider>
          <InnerSidebar />
          <SidebarInset className="overflow-hidden">{children}</SidebarInset>
        </ServiceInnerSidebarProvider>
      </ServiceSidebarProvider>
    </SidebarProvider>
  );
}
