import {
  useDraw,
  useFeatureActions,
  useLayer,
  useMap,
} from "@geon-map/react-odf";
import { crtfckey, WMS_URL } from "@geon-query/model";
import { useMemo, useState } from "react";

import { useHighlightLayer } from "../../hooks/use-highlight-layer";

// 타입 정의
export type GeoJsonFeatureCollection = {
  type: "FeatureCollection";
  features: any[];
  totalFeatures: number;
  numberMatched: number;
  numberReturned: number;
  timeStamp: string;
  crs: { type: string; properties: { name: string } };
  bbox: [number, number, number, number];
};

export type DrawingModeId = "lineString" | "polygon" | "point" | "circle";

const drawingTools: Array<{
  id: DrawingModeId;
  label: string;
  emoji: string;
  className: string;
}> = [
  {
    id: "lineString",
    label: "직선",
    emoji: "📏",
    className: "bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700",
  },
  {
    id: "polygon",
    label: "다각형",
    emoji: "⬟",
    className: "bg-green-50 hover:bg-green-100 border-green-200 text-green-700",
  },
  {
    id: "point",
    label: "점",
    emoji: "📍",
    className:
      "bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700",
  },
  {
    id: "circle",
    label: "원",
    emoji: "⭕",
    className:
      "bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700",
  },
];

export interface SpatialSearchWidgetProps {
  targetLayerId: string;
  resultLayerId?: string;
  className?: string;
  onResult?(payload: {
    result: GeoJsonFeatureCollection;
    odfFeatureList: any[];
  }): void;
}

export function SpatialSearchWidget({
  targetLayerId,
  className,
  onResult,
}: SpatialSearchWidgetProps) {
  const { mode: drawingMode, isDrawing, stopDrawing, startDrawing } = useDraw();
  const { clearHighlight, highlights, highlight } = useHighlightLayer();
  const { findLayer, addLayer, drawLayer, layerFactory } = useLayer();
  const { map, isLoading } = useMap();
  const { deleteFeature } = useFeatureActions();

  if (!isLoading || !map) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  // layerFactory는 이제 useLayer에서 직접 가져옴

  const [targetOdfLayerId, setTargetOdfLayerId] = useState<string | null>(null);
  const [errMsg, setErrMsg] = useState<string | null>(null);

  const targetLayer = useMemo(
    () =>
      targetOdfLayerId
        ? (findLayer(targetOdfLayerId as string)?.odfLayer ?? null)
        : null,
    [findLayer, targetOdfLayerId],
  );

  const getLayerEpsg = (): string | undefined => {
    try {
      const opt = targetLayer?.getInitialOption?.();
      const projStr: string | undefined = opt?.params?.projection;
      if (projStr?.includes(":")) return projStr.split(":")[1];
    } catch {
      // noop
    }
    return undefined;
  };

  const handleAddTargetLayer = async () => {
    setErrMsg(null);
    try {
      if (targetOdfLayerId) {
        const exists = findLayer(targetOdfLayerId);
        if (exists?.odfLayer) {
          exists.odfLayer.setVisible?.(true);
          exists.odfLayer.setZIndex?.(5000);
          return;
        }
      }
      const createdId = await addLayer({
        type: "geoserver",
        server: { url: WMS_URL },
        layer: targetLayerId,
        info: { lyrId: targetLayerId, lyrNm: "샘플" },
        crtfckey,
        service: "wms",
        method: "post",
        visible: true,
      });
      setTargetOdfLayerId(createdId as string);
    } catch (e: any) {
      console.error(e);
      setErrMsg("대상 레이어를 추가하지 못했습니다.");
    }
  };

  const handleToolClick = (toolId: DrawingModeId) => {
    setErrMsg(null);
    if (!map) {
      setErrMsg("지도가 아직 준비되지 않았습니다.");
      return;
    }
    if (drawingMode === toolId) {
      stopDrawing();
      return;
    }

    // startDrawing이 drawend 콜백을 반환하는 구조 활용
    const { drawend } = startDrawing(toolId);

    drawend(async (feature: any) => {
      try {
        if (!targetLayer) {
          setErrMsg(
            "대상 레이어가 없습니다. 먼저 '대상 레이어 추가'를 눌러주세요.",
          );
          return;
        }
        // 좌표계 보정: 레이어 EPSG 기준으로 서버에 보낼 도형 변환
        const projection = map.getProjection?.();
        const epsg = getLayerEpsg();
        const clone = feature.clone();
        const unproject = epsg ? projection.unprojectGeom(clone, epsg) : clone;

        // 공간검색 실행 (INTERSECTS)
        const { result, odfFeatureList } = await layerFactory!.spatialQuery(
          targetLayer,
          {
            feature: unproject,
            proj: epsg,
            spatialOperator: "INTERSECTS",
          },
        );

        // 결과 표출(서버 피처 + 사용자가 그린 도형 시각화)
        highlights(odfFeatureList as [], { isFitToLayer: false, clear: false });
        highlight(feature.clone(), { isFitToLayer: false, clear: false });
        onResult?.({
          result: result as GeoJsonFeatureCollection,
          odfFeatureList,
        });
      } catch (e: any) {
        console.error(e);
        setErrMsg(e?.message ?? "공간검색 중 오류가 발생했습니다.");
      } finally {
        // ✅ 공간검색 완료 후 그리기 feature 삭제 - useFeatureActions 사용
        if (feature) {
          setTimeout(() => {
            console.log("🔍 [spatial-search] 그리기 feature 삭제 시도", {
              featureId: feature.getId?.(),
              featureType: feature.getGeometry?.()?.getType?.(),
              hasDrawLayer: !!drawLayer,
              drawLayerId: drawLayer?.id,
              component: "spatial-search-widget",
            });

            const success = deleteFeature(feature, "draw");
            if (success) {
              console.log(
                "✅ [spatial-search] useFeatureActions로 그리기 feature 삭제 완료",
              );
            } else {
              console.warn(
                "⚠️ [spatial-search] useFeatureActions로 그리기 feature 삭제 실패",
              );
            }
          }, 0);
        }
      }
    });
  };

  return (
    <div className={`absolute right-20 top-20 z-10 w-64 ${className ?? ""}`}>
      <div className="rounded-lg border bg-white shadow-lg">
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">공간검색</h3>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            소스 레이어: <span className="font-mono">{targetLayerId}</span>
          </p>
          {targetOdfLayerId && (
            <p className="mt-1 text-[11px] text-gray-500">
              생성된 레이어 ID:{" "}
              <span className="font-mono">{targetOdfLayerId}</span>
            </p>
          )}

          <div className="mt-3">
            <button
              onClick={handleAddTargetLayer}
              className="w-full rounded-md border border-slate-300 bg-slate-50 px-3 py-2 text-sm font-medium text-slate-700 hover:bg-slate-100"
              title="대상 레이어를 지도에 올립니다"
            >
              ➕ 대상 레이어 추가
            </button>
          </div>
        </div>

        <div className="p-4">
          <div className="mb-4">
            <h4 className="mb-2 text-sm font-medium text-gray-700">
              검색 도구
            </h4>
            <div className="grid grid-cols-2 gap-2">
              {drawingTools.map((tool) => (
                <button
                  key={tool.id}
                  onClick={() => handleToolClick(tool.id)}
                  className={`flex items-center gap-2 rounded-md border px-3 py-2 text-sm font-medium transition-colors
                    ${drawingMode === tool.id ? "ring-2 ring-blue-500 ring-offset-1" : ""}
                    ${tool.className}
                  `}
                  title={`${tool.label} 그리기`}
                >
                  <span className="text-base">{tool.emoji}</span>
                  {tool.label}
                  {drawingMode === tool.id && (
                    <span className="ml-auto text-xs">●</span>
                  )}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <button
              onClick={() => {
                clearHighlight();
              }}
              className="w-full rounded-md bg-red-50 px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-100"
              title="결과 지우기"
            >
              🗑️ 초기화
            </button>

            {isDrawing && (
              <div className="rounded-md border border-amber-200 bg-amber-50 p-2 text-xs text-amber-800">
                도형을 완성하면 공간검색이 실행됩니다.
              </div>
            )}

            {errMsg && (
              <div className="rounded-md border border-red-200 bg-red-50 p-2 text-xs text-red-700">
                {errMsg}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
