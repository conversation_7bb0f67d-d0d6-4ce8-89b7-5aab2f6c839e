import React, { useEffect } from "react";

import { MapStoreProvider } from "../contexts/map-store-context";
import { useMap } from "../hooks/use-map";
import { useMapActions } from "../hooks/use-map-actions";
import { cn } from "../lib/utils";
import type { MapInitializeOptions, UseMapReturn } from "../types/map-types";

interface SplitMode {
  count: 1 | 2 | 3 | 4;
}

interface MapInital extends Partial<MapInitializeOptions> {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  containerRef?: React.RefObject<HTMLDivElement>;
  autoInit?: boolean;
  onMapInit?: (mapState: UseMapReturn) => void;

  splitMode?: SplitMode;
}

/**
 * Map 컴포넌트 (Components 레이어)
 * useMap, useMapActions 훅을 사용하여 ODF 지도를 초기화하고 상태를 관리합니다.
 * hook 또는 해당 컴포넌트를 사용하여 지도를 렌더링할 수 있습니다.
 *
 * @example
 * ```tsx
 * // 단일 지도
 * <Map className="w-full h-96" center={[127, 37]}>
 *   <DrawProvider />
 * </Map>
 * ```
 */

// 분할 지도 위치 정보
type Position = "top-left" | "top-right" | "bottom-left" | "bottom-right";

// 분할 그리드 설정 함수
const getGridConfig = (count: number) => {
  switch (count) {
    case 1:
      return {
        gridClass: "grid-cols-1 grid-rows-1",
        positions: ["top-left"] as Position[],
      };
    case 2:
      return {
        gridClass: "grid-cols-2 grid-rows-1",
        positions: ["top-left", "top-right"] as Position[],
      };
    case 3:
      return {
        gridClass: "grid-cols-3 grid-rows-1",
        positions: ["top-left", "top-right", "bottom-left"] as Position[],
      };
    case 4:
      return {
        gridClass: "grid-cols-2 grid-rows-2",
        positions: [
          "top-left",
          "top-right",
          "bottom-left",
          "bottom-right",
        ] as Position[],
      };
    default:
      return {
        gridClass: "grid-cols-1 grid-rows-1",
        positions: ["top-left"] as Position[],
      };
  }
};

// 개별 지도 뷰 컴포넌트
interface MapViewProps extends Partial<MapInitializeOptions> {
  position: Position;
  onMapInit?: (mapState: UseMapReturn) => void;
  children?: React.ReactNode;
  showPositionLabel?: boolean; // 위치 라벨 표시 여부
}

const MapView = ({
  position,
  onMapInit,
  children,
  center,
  zoom,
  projection,
  ...mapInitializeOptions
}: MapViewProps) => {
  const id = React.useId();
  const containerRef = React.useRef<HTMLDivElement>(null);
  const initializedRef = React.useRef(false);

  const { isLoading, error } = useMap({
    containerRef,
    center,
    zoom,
    projection,
    ...mapInitializeOptions,
    onMapInit,
  });

  // 디버깅: MapView 상태 추적
  React.useEffect(() => {
    console.log(`📊 MapView ${id} 상태:`, {
      position,
      isLoading,
      error,
      hasContainer: !!containerRef.current,
    });
  }, [isLoading, error, position, id]);

  const { setCenter, setZoom } = useMapActions();

  useEffect(() => {
    if (!isLoading && !error && !initializedRef.current) {
      initializedRef.current = true;
      if (center && setCenter) {
        setCenter(center);
      }
      if (zoom !== undefined && setZoom) {
        setZoom(zoom);
      }
    }
  }, [isLoading, error, center, zoom, setCenter, setZoom]);

  if (error) {
    return (
      <div className="relative h-full w-full flex items-center justify-center">
        <div className="text-red-500">지도 로딩 실패: {error}</div>
      </div>
    );
  }

  return (
    <div className="relative h-full w-full">
      <div id={`map-${id}`} ref={containerRef} className="w-full h-full" />
      {!isLoading && <>{children}</>}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
          <div className="text-gray-600">지도 로딩 중...</div>
        </div>
      )}
    </div>
  );
};

// Map 인스턴스 pool 관리 (ID 안정화)
const useMapPool = () => {
  const [mapInstances] = React.useState(() => {
    const stableId = `map_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // React Strict Mode 감지 (개발 환경에서 이중 실행)
    const isStrictMode = process.env.NODE_ENV === "development";
    console.log(
      `🏗️ MapPool 생성: ${stableId}${isStrictMode ? " (Strict Mode)" : ""}`,
    );

    return Array.from({ length: 4 }, (_, index) => ({
      id: `${stableId}-${index}`,
      index,
      position: (
        ["top-left", "top-right", "bottom-left", "bottom-right"] as Position[]
      )[index],
    }));
  });

  // 디버깅: MapPool 사용 추적
  React.useEffect(() => {
    if (mapInstances.length > 0 && mapInstances[0]) {
      const poolId = mapInstances[0].id.split("-")[0];
      console.log(
        `🔄 MapPool 사용: ${poolId} (인스턴스 ${mapInstances.length}개)`,
      );
    }
  }, [mapInstances]);

  return mapInstances;
};

/**
 * Map 컴포넌트 - react-odf 내부에서만 MapStoreProvider 사용
 * 사용자는 단순히 <Map> 태그만 사용하면 됩니다.
 */
export const Map = ({
  className,
  style,
  children,
  onMapInit,
  center,
  zoom,
  projection,
  splitMode = { count: 1 }, // 기본값을 count=1로 설정
  ...mapInitializeOptions
}: MapInital) => {
  const mapPool = useMapPool();

  // 현재 분할 모드에 따른 그리드 설정
  const { gridClass, positions } = getGridConfig(splitMode.count);

  return (
    <div className={cn("relative", className)} style={style}>
      <div className={`grid ${gridClass} gap-1 h-full w-full`}>
        {mapPool.slice(0, splitMode.count).map((mapInstance, index) => {
          const actualPosition = positions[index] ?? mapInstance.position;

          // 디버깅: MapStoreProvider 생성 추적
          console.log(`🔄 Map ${index} (${mapInstance.id}):`, {
            splitCount: splitMode.count,
            position: actualPosition,
            isActive: index < splitMode.count,
          });

          // TypeScript를 위한 타입 검증 (실제로는 항상 유효함)
          if (!actualPosition) {
            console.error(`Invalid position for map ${index}`);
            return null;
          }

          return (
            <div
              key={mapInstance.id} // 절대 변하지 않는 stable key
              className={
                splitMode.count === 1
                  ? "relative"
                  : "relative border border-gray-300"
              }
            >
              <MapStoreProvider mapId={mapInstance.id}>
                <MapView
                  center={center}
                  zoom={zoom}
                  projection={projection}
                  position={actualPosition}
                  onMapInit={onMapInit}
                  showPositionLabel={splitMode.count > 1} // 단일 지도일 때는 위치 라벨 숨김
                  {...mapInitializeOptions}
                >
                  {children}
                </MapView>
              </MapStoreProvider>
            </div>
          );
        })}
      </div>
    </div>
  );
};
