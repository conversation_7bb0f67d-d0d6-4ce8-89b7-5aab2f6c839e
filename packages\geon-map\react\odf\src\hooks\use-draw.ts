import { DrawingMode } from "@geon-map/core";
import { useCallback } from "react";

import { useStores } from "../contexts/map-store-context";
import { useDrawProviderStatus } from "../providers/draw/draw-provider";
import { changeFeatureStyle } from "../utils/feature-actions";
import { useEvent } from "./use-event";
import { useLayer } from "./use-layer";
import { useMap } from "./use-map";

/**
 * 그리기 및 측정 기능 hook
 */

// 내보내기/가져오기 옵션 타입
export interface ExportImportOptions {
  /** 데이터 투영 코드 */
  dataProjectionCode?: string;
  /** 피처 투영 코드 */
  featureProjectionCode?: string;
  /** 파일 다운로드 여부 (KML 내보내기 시) */
  downloadFile?: boolean;
}

// 가져오기 결과 타입
export interface ImportResult {
  success: boolean;
  drawFeatureCount: number;
  measureFeatureCount: number;
  errors?: string[];
}

export interface UseDrawNewOptions {
  /** 피처 선택 기능 활성화 */
  enableSelection?: boolean;
  /** 컨텍스트 메뉴 기능 활성화 */
  enableContextMenu?: boolean;
  /** 다중 선택 허용 */
  multiSelect?: boolean;
  /** 피처 삭제 시 콜백 */
  onFeatureDelete?: (feature: any) => void;
  /** 피처 편집 시 콜백 */
  onFeatureEdit?: (feature: any) => void;
}

// drawEnd 콜백 타입
export type DrawEndCallback = (feature: any, eventId: string) => void;

// drawEnd 반환 타입
export interface DrawEndResult {
  drawend: (callback: DrawEndCallback) => void;
}

export function useDraw(options: UseDrawNewOptions = {}) {
  const {
    enableSelection = true,
    enableContextMenu = true,
    multiSelect = false,
  } = options;

  // 🎯 Context 기반 스토어 선택 (다중 지도 인스턴스 지원)
  const { drawStore, layerStore } = useStores();

  const { registerListener } = useEvent();
  const { drawLayer, measureLayer, toGeoJson, clearFeatures } = useLayer();
  const { isProviderSet } = useDrawProviderStatus();

  // Map과 ODF 인스턴스 가져오기
  const { map, isLoading, error } = useMap();

  if (isLoading || error || !map) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  const layerFactory = layerStore((state: any) => state.layerFactory);

  // 만약 성능 문제가 발생한다면 선택자를 사용하여 필요한 값만 추출하여 사용하세요.
  const {
    drawCore,
    measureCore,
    clearCore,
    drawControl,
    measureControl,
    clearControl,
    isReady: drawIsReady,
    error: drawError,
    isDrawing,
    mode,
    drawnFeatures, // ✅ deprecated: Feature
    measureResults, // ✅ deprecated: Feature
    startDrawing: startDrawingAction,
    stopDrawing: stopDrawingAction,
    getDebugInfo,
    drawStyle,
    measureStyle,
  } = drawStore() as any;

  /**
   * startDrawingWithCallback - 그리기/측정 시작 + 완료 이벤트 처리
   */
  const startDrawingWithCallback = useCallback(
    (mode: DrawingMode = "point"): DrawEndResult => {
      let callbackFn: DrawEndCallback | null = null;

      // 측정 모드인지 확인
      const isMeasureMode =
        typeof mode === "string" && mode.startsWith("measure-");

      if (isMeasureMode) {
        // 측정 모드 처리
        if (!measureControl) {
          console.warn(
            "⚠️ 측정 기능을 사용할 수 없습니다. MeasureControl이 초기화되지 않음",
          );
          return {
            drawend() {
              console.warn("측정 기능이 비활성화된 상태입니다.");
            },
          };
        }

        // 측정 시작 (Store Action 사용)
        startDrawingAction(mode);

        // 고유한 이벤트 ID 생성
        const eventId = `measureend_${Date.now()}_${Math.random()}`;

        // drawend 이벤트 등록 (측정도 drawend 이벤트 사용)
        registerListener(
          measureControl,
          "drawend", // 측정도 drawend 이벤트 사용
          (feature: any) => {
            // 측정 상태만 업데이트 (measureCore.stopMeasuring() 호출하지 않음)
            drawStore.getState().handleMeasureEnd(feature);

            if (callbackFn) {
              callbackFn(feature, eventId);
            }
          },
          { once: true, listenerId: eventId },
        );
      } else {
        // 그리기 모드 처리
        if (!drawControl) {
          console.warn(
            "⚠️ 그리기 기능을 사용할 수 없습니다. DrawControl이 초기화되지 않음",
          );
          return {
            drawend() {
              console.warn("그리기 기능이 비활성화된 상태입니다.");
            },
          };
        }

        // 그리기 시작 (Store Action 사용)
        startDrawingAction(mode as DrawingMode);

        // 고유한 이벤트 ID 생성
        const eventId = `drawend_${Date.now()}_${Math.random()}`;

        // drawend 이벤트 등록
        registerListener(
          drawControl,
          "drawend",
          (feature: any) => {
            // 그리기 상태 업데이트
            stopDrawingAction();

            if (callbackFn) {
              callbackFn(feature, eventId);
            }
          },
          { once: true, listenerId: eventId },
        );
      }

      return {
        drawend(fn: DrawEndCallback) {
          callbackFn = fn;
        },
      };
    },
    [
      drawControl,
      measureControl,
      startDrawingAction,
      stopDrawingAction,
      registerListener,
    ],
  );

  /**
   * Core별 상태 확인 유틸리티 (DrawProvider 설정 여부 포함)
   */
  const coreStatus = {
    // Provider 설정 상태
    isProviderSet,
    providerMessage: isProviderSet
      ? "DrawProvider가 설정되어 있습니다."
      : "⚠️ DrawProvider가 설정되지 않았습니다. 그리기 기능을 사용하려면 <DrawProvider>를 추가하세요.",

    // Core별 상태
    draw: {
      available: !!drawCore,
      ready: !!drawCore && !!drawControl,
    },
    measure: {
      available: !!measureCore,
      ready: !!measureCore && !!measureControl,
    },
    clear: {
      available: !!clearCore,
      ready: !!clearCore && !!clearControl,
    },
    // 종합 상태
    isReady: isProviderSet ? !isLoading && !error : false,
    canDraw: isProviderSet && !isLoading && !error && !!drawCore,
    canMeasure: isProviderSet && !isLoading && !error && !!measureCore,
    statusSummary: isProviderSet
      ? !isLoading && !error
        ? "✅ 그리기 기능 사용 가능"
        : "⏳ 초기화 중..."
      : "❌ DrawProvider 설정 필요",
  };

  // === 내보내기/가져오기 기능 구현 ===
  // 공통: 특정 레이어 집합을 KML로 내보내기 (type 태깅 포함)
  const exportLayersToKML = useCallback(
    (
      targets: ("draw" | "measure")[],
      options: ExportImportOptions = {},
    ): Promise<string | null> => {
      const { downloadFile = false } = options;

      if (!layerFactory) {
        console.warn("LayerFactory가 준비되지 않았습니다.");
        return Promise.resolve(null);
      }
      if (!drawLayer && !measureLayer) {
        console.warn("내보낼 그리기/측정 레이어가 없습니다.");
        return Promise.resolve(null);
      }

      try {
        const tempLayer = layerFactory.produce("empty", {});
        const exportProjection = options?.dataProjectionCode ?? "EPSG:4326";

        targets.forEach((item) => {
          const src =
            item === "draw" ? drawLayer?.odfLayer : measureLayer?.odfLayer;
          if (!src) return;

          const features = src.getFeatures() || [];
          const clones = features.map((feature: any) => {
            const clone = feature.clone();
            clone.set("type", item);
            clone.setStyle(feature.getStyle());
            const proj =
              map?.getProjection?.() || map?.getView().getProjection();
            const code =
              typeof exportProjection === "string"
                ? exportProjection.replace(/[^0-9]/g, "")
                : String(exportProjection);
            proj?.unprojectGeom?.(clone, code);
            return clone;
          });

          if (clones.length > 0) tempLayer.addFeatures(clones);
        });

        tempLayer.setOpacity(0);
        tempLayer.setMap(map);
        const kml = tempLayer.toKML(downloadFile);
        tempLayer.removeMap(map);
        return Promise.resolve(kml || null);
      } catch (error) {
        console.error("KML 내보내기 실패:", error);
        return Promise.resolve(null);
      }
    },
    [layerFactory, drawLayer, measureLayer],
  );

  // Granular + Combined API
  // const exportDrawToKML = useCallback(
  //   (options?: ExportImportOptions) => exportLayersToKML(["draw"], options),
  //   [exportLayersToKML]
  // );
  // const exportMeasureToKML = useCallback(
  //   (options?: ExportImportOptions) => exportLayersToKML(["measure"], options),
  //   [exportLayersToKML]
  // );
  const exportAllToKML = useCallback(
    (options?: ExportImportOptions) =>
      exportLayersToKML(["draw", "measure"], options),
    [exportLayersToKML],
  );
  /**
   * 그리기/측정 데이터를 GeoJSON으로 내보내기
   */
  const exportToGeoJSON = useCallback((): any | null => {
    if (!drawLayer && !measureLayer) {
      console.warn("내보낼 그리기/측정 레이어가 없습니다.");
      return null;
    }

    try {
      const result = {
        type: "FeatureCollection",
        features: [] as any[],
      };

      // 그리기 레이어 내보내기
      if (drawLayer) {
        const drawGeoJSON = toGeoJson(drawLayer.id);
        if (drawGeoJSON?.features) {
          // 피처에 type 속성 추가
          const drawFeatures = drawGeoJSON.features.map((feature: any) => ({
            ...feature,
            properties: {
              ...feature.properties,
              type: "draw",
            },
          }));
          result.features.push(...drawFeatures);
        }
      }

      // 측정 레이어 내보내기
      if (measureLayer) {
        const measureGeoJSON = toGeoJson(measureLayer.id);
        if (measureGeoJSON?.features) {
          // 피처에 type 속성 추가
          const measureFeatures = measureGeoJSON.features.map(
            (feature: any) => ({
              ...feature,
              properties: {
                ...feature.properties,
                type: "measure",
              },
            }),
          );
          result.features.push(...measureFeatures);
        }
      }

      return result.features.length > 0 ? result : null;
    } catch (error) {
      console.error("GeoJSON 내보내기 실패:", error);
      return null;
    }
  }, [drawLayer, measureLayer, toGeoJson]);

  /**
   * KML 데이터를 그리기/측정 레이어로 가져오기
   */
  const importFromKML = useCallback(
    (
      kml: string,
      options: ExportImportOptions = {},
      onResult?: (result: ImportResult) => void,
    ): ImportResult => {
      const {
        dataProjectionCode = "EPSG:4326", // 내보내기 기본 투영과 일치
        featureProjectionCode = map?.getView().getProjection().getCode?.() ??
          "EPSG:3857",
      } = options;

      const result: ImportResult = {
        success: false,
        drawFeatureCount: 0,
        measureFeatureCount: 0,
        errors: [],
      };

      if (!layerFactory) {
        result.errors?.push("LayerFactory가 초기화되지 않았습니다.");
        onResult?.(result);
        return result;
      }

      if (!drawLayer && !measureLayer) {
        result.errors?.push("가져올 그리기/측정 레이어가 없습니다.");
        onResult?.(result);
        return result;
      }

      try {
        // KML 레이어 생성 (LayerFactory 사용)
        const kmlLayer = layerFactory.produce("kml", {
          data: kml,
          dataProjectionCode,
          featureProjectionCode,
          defaultStyleOption: {
            text: {
              fill: { color: [255, 0, 0, 1] },
              stroke: { color: [255, 255, 255, 1] },
              font: "20px sans-serif",
              textAlign: "left",
            },
          },
        });

        if (!kmlLayer) {
          result.errors?.push("KML 파싱에 실패했습니다.");
          onResult?.(result);
          return result;
        }

        // 임시로 지도에 추가하여 피처 로드
        kmlLayer.setOpacity(0);
        kmlLayer.setMap(map);

        // TODO 위에까지 const layerId = await addLayer('kml', { data: kml, ... }, opacity: 0) 이런 형태로 바꿀 수 있어야함
        // TODO 메서드 체이닝 대신 하위 객체에 더 안전하게 접근하도록 해야하지 않을까..?
        const features = kmlLayer.getFeatures();

        // feature 타입정의로 안전하게 접근가능해야함
        features.forEach((feature: any) => {
          const type = feature.get("type");

          // 🔧 Issue 2 Fix: Provider 스타일 적용 개선
          const styleToApply = type === "measure" ? measureStyle : drawStyle;

          const geometryType = feature.getGeometry?.()?.getType?.();

          console.log("🎨 Import 스타일 적용:", {
            featureType: type,
            styleToApply,
            geometryType,
          });

          // 🔧 Issue 2 Fix: Circle geometry 왜곡 방지
          if (geometryType === "Circle") {
            console.log("🔵 Circle geometry 감지 - 투영 변환 최적화 적용");
            // Circle의 경우 중심점과 반지름을 보존하여 재생성
            const geometry = feature.getGeometry();
            const center = geometry.getCenter();
            const radius = geometry.getRadius();

            // 새로운 Circle geometry 생성 (투영 변환 없이)
            const newCircle = new (window as any).odf.Geometry.Circle(
              center,
              radius,
            );
            feature.setGeometry(newCircle);
          }

          // 대상 레이어로 피처 이동 (스타일 적용 전에 레이어에 추가)
          if (type === "draw" && drawLayer) {
            drawLayer.odfLayer.addFeature(feature);
            result.drawFeatureCount++;
          } else if (type === "measure" && measureLayer) {
            measureLayer.odfLayer.addFeature(feature);
            result.measureFeatureCount++;
          } else if (drawLayer) {
            // type이 없는 경우 기본적으로 그리기 레이어에 추가
            drawLayer.odfLayer.addFeature(feature);
            result.drawFeatureCount++;
          }

          // 레이어에 추가한 후 스타일 적용 (더 안정적)
          if (styleToApply) {
            try {
              const success = changeFeatureStyle(feature, styleToApply);
              console.log(`🎨 스타일 적용 ${success ? "성공" : "실패"}:`, type);
            } catch (error) {
              console.warn("🎨 스타일 적용 중 오류:", error);
            }
          }
        });

        // 임시 레이어 제거
        kmlLayer.removeMap(map);

        result.success = true;
        onResult?.(result);
      } catch (error) {
        console.error("KML 가져오기 실패:", error);
        result.errors?.push(`KML 가져오기 실패: ${error}`);
        onResult?.(result);
      }

      return result;
    },
    [layerFactory, drawLayer, measureLayer],
  );

  /**
   * GeoJSON 데이터를 그리기/측정 레이어로 가져오기
   */
  const importFromGeoJSON = useCallback(
    (geoJson: any, onResult?: (result: ImportResult) => void): ImportResult => {
      const result: ImportResult = {
        success: false,
        drawFeatureCount: 0,
        measureFeatureCount: 0,
        errors: [],
      };

      if (!layerFactory) {
        result.errors?.push("LayerFactory가 초기화되지 않았습니다.");
        onResult?.(result);
        return result;
      }

      if (!drawLayer && !measureLayer) {
        result.errors?.push("가져올 그리기/측정 레이어가 없습니다.");
        onResult?.(result);
        return result;
      }

      try {
        // GeoJSON 임시 레이어 생성 (LayerFactory 사용)
        const geoJsonLayer = layerFactory!.produce("geojson", {
          data: geoJson,
          dataProjectionCode: "EPSG:4326",
          featureProjectionCode:
            map?.getView().getProjection().getCode?.() ?? "EPSG:3857",
        });

        if (!geoJsonLayer) {
          result.errors?.push("GeoJSON 파싱에 실패했습니다.");
          onResult?.(result);
          return result;
        }

        // 임시로 지도에 추가하여 features 추출
        geoJsonLayer.setOpacity(0);
        geoJsonLayer.setMap(map);

        // features를 draw/measure layer로 이동
        const features = geoJsonLayer.getFeatures();

        features.forEach((feature: any) => {
          const geometryType = feature.getGeometry().getType().toLowerCase();
          const type =
            feature.get("type") ||
            (geometryType.includes("point") ? "draw" : "draw");

          console.log("🎨 GeoJSON Import 스타일 적용:", {
            featureType: type,
            geometryType,
            hasDrawStyle: !!drawStyle,
            hasMeasureStyle: !!measureStyle,
          });

          // 🔧 Issue 2 Fix: Circle geometry 왜곡 방지 (GeoJSON)
          if (geometryType === "circle") {
            console.log(
              "🔵 GeoJSON Circle geometry 감지 - 투영 변환 최적화 적용",
            );
            const geometry = feature.getGeometry();
            const center = geometry.getCenter();
            const radius = geometry.getRadius();

            // 새로운 Circle geometry 생성 (투영 변환 없이)
            const newCircle = new (window as any).odf.Geometry.Circle(
              center,
              radius,
            );
            feature.setGeometry(newCircle);
          }

          // 적절한 레이어에 피처 추가 (스타일 적용 전에)
          if (type === "draw" && drawLayer) {
            drawLayer.odfLayer.addFeature(feature);
            result.drawFeatureCount++;
          } else if (type === "measure" && measureLayer) {
            measureLayer.odfLayer.addFeature(feature);
            result.measureFeatureCount++;
          } else if (drawLayer) {
            drawLayer.odfLayer.addFeature(feature);
            result.drawFeatureCount++;
          }

          // 🔧 Issue 2 Fix: 레이어에 추가한 후 스타일 적용
          const styleToApply = type === "measure" ? measureStyle : drawStyle;
          if (styleToApply) {
            try {
              const success = changeFeatureStyle(feature, styleToApply);
              console.log(
                `🎨 GeoJSON 스타일 적용 ${success ? "성공" : "실패"}:`,
                type,
              );
            } catch (error) {
              console.warn("🎨 GeoJSON 스타일 적용 중 오류:", error);
            }
          }
        });

        // 임시 레이어 제거
        geoJsonLayer.removeMap(map);

        result.success = true;
        onResult?.(result);
      } catch (error) {
        console.error("GeoJSON 가져오기 실패:", error);
        result.errors?.push(`GeoJSON 가져오기 실패: ${error}`);
        onResult?.(result);
      }

      return result;
    },
    [layerFactory, drawLayer, measureLayer],
  );

  /**
   * 모든 그리기/측정 데이터 삭제
   */
  const clearAll = useCallback(() => {
    try {
      if (drawLayer) {
        clearFeatures(drawLayer.id);
      }
      if (measureLayer) {
        clearFeatures(measureLayer.id);
      }
      return true;
    } catch (error) {
      console.error("데이터 삭제 실패:", error);
      return false;
    }
  }, [drawLayer, measureLayer, clearFeatures]);

  return {
    // 🎯 4개 Core 상태 (단일책임 분리)
    drawCore,
    measureCore,
    clearCore,
    drawControl,
    measureControl,
    clearControl,
    isReady: coreStatus.isReady,
    error: drawError,

    // 통합된 그리기/측정 상태
    isDrawing,
    mode,
    isMeasureMode: mode?.startsWith("measure-") || false,

    // ✅ deprecated: Feature
    drawnFeatures,
    measureResults,

    // Store Actions
    startDrawing: startDrawingWithCallback,
    stopDrawing: stopDrawingAction,

    // Core 상태 확인
    coreStatus,

    // 개발자 도구
    getDebugInfo,

    // === 내보내기/가져오기 기능 ===
    exportToKML: exportAllToKML,
    exportToGeoJSON,
    importFromKML,
    importFromGeoJSON,
    clearAll,

    // 설정
    options: {
      enableSelection,
      enableContextMenu,
      multiSelect,
    },
  };
}
