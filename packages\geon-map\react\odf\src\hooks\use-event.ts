import { useCallback, useEffect } from "react";

import { useEventStore } from "../stores/event-store";
import { useMap } from "./use-map";

// 🆕 전역 타입 선언
declare global {
  var __layerDetectionQueue: Map<string, LayerQueueItem> | undefined;
  var __globalContextMenuRegistered: boolean | undefined;
  var __layerDetectionCallbacks:
    | Array<(result: LayerDetectionResult) => void>
    | undefined;
  var __globalContextMenuCleanup: (() => void) | undefined;
}

/**
 * 🎯 범용 이벤트 관리 훅 (Store 기반)
 *
 * 모든 이벤트 등록/해제를 Store에서 관리하여
 * React 상태 변경 감지와 코드 재사용성을 향상시킵니다.
 *
 * @example
 * ```tsx
 * // 사용자 앱에서 마우스 좌표 추적
 * function MyComponent() {
 *   const { registerListener } = useEvent();
 *   const { mouseCoordinates } = useEventState();
 *
 *   useEffect(() => {
 *     const cleanup = registerListener(
 *       map,
 *       'pointermove',
 *       (event) => {
 *         const coord = map.getCoordinateFromPixel(event.pixel);
 *         // Store에 저장하거나 직접 처리
 *       }
 *     );
 *     return cleanup;
 *   }, [map]);
 * }
 * ```
 */

/**
 * 지원하는 이벤트 타입들
 */
export type EventType =
  | "click"
  | "dblclick"
  | "contextmenu"
  | "pointermove"
  | "pointerleave"
  | "moveend"
  | "drawstart"
  | "drawend"
  | "featureselect"
  | "change:center"
  | "change:resolution";

/**
 * 🆕 레이어 기반 Feature 정보
 */
export interface LayerFeatureInfo {
  layer: any;
  layerId: string;
  layerType: string;
  feature: any;
  featureId: string;
  featureType: string;
  coordinates: any[];
  properties: any;
}

/**
 * 🆕 레이어 감지 결과
 */
export interface LayerDetectionResult {
  layerFeature: LayerFeatureInfo | null;
  pixel: [number, number];
  position: [number, number];
  allFeatures: Array<{ layer: any; feature: any }>;
}

/**
 * 🆕 레이어 큐 항목
 */
export interface LayerQueueItem {
  layer: any;
  layerId: string;
  layerType: string;
  priority: number;
}

/**
 * 이벤트 핸들러 타입
 */
export type EventHandler<T = any> = (event: T) => void;

/**
 * 이벤트 리스너 옵션
 */
export interface EventListenerOptions {
  /** 일회성 이벤트인지 여부 */
  once?: boolean;
  /** 이벤트 활성화 여부 */
  enabled?: boolean;
  /** 리스너 ID (자동 생성됨) */
  listenerId?: string;
}

/**
 * 🎯 범용 이벤트 관리 훅 (Store 기반)
 *
 * 이벤트 등록/해제를 Store에서 관리하여 상태 추적과 정리를 자동화합니다.
 * 특정 기능(좌표, 스케일 등)은 사용자 앱에서 이 훅을 사용해 구현하세요.
 */
export function useEvent() {
  const eventInstance = useEventStore((state) => state.eventInstance);
  const { map } = useMap();

  // 🆕 레이어 감지 큐 (전역 상태) - 의존성 없음
  const layerQueue = () => {
    if (!globalThis.__layerDetectionQueue) {
      globalThis.__layerDetectionQueue = new Map<string, LayerQueueItem>();
    }
    return globalThis.__layerDetectionQueue as Map<string, LayerQueueItem>;
  };

  /**
   * 이벤트 리스너 등록 (Store 기반) - 의존성 최소화
   */
  const registerListener = useCallback(
    (
      target: any,
      eventType: EventType,
      handler: EventHandler,
      options: EventListenerOptions = {},
    ) => {
      const currentEventInstance = useEventStore.getState().eventInstance;

      // console.log("🎯 registerListener 호출됨:", {
      //   eventType,
      //   target: !!target,
      //   eventInstance: !!currentEventInstance,
      // });

      if (!currentEventInstance) {
        console.warn("EventInstance is not available");
        return () => {};
      }

      const { once = false, enabled = true, listenerId } = options;

      if (!enabled) {
        // console.log("🎯 registerListener: enabled가 false입니다");
        return () => {};
      }

      try {
        // console.log("🎯 eventStore.registerEventListener 호출 중...");
        const result = useEventStore
          .getState()
          .registerEventListener(target, eventType as any, handler, {
            listenerId,
            once,
          });

        // console.log(
        //   "🎯 eventStore.registerEventListener 결과:",
        //   !!result?.cleanup
        // );
        return result.cleanup;
      } catch (error) {
        console.error("Failed to register event listener:", error);
        return () => {};
      }
    },
    [], // 의존성 제거
  );

  /**
   * 특정 리스너 제거 - 의존성 최소화
   */
  const unregisterListener = useCallback(
    (listenerId: string) => {
      const currentEventInstance = useEventStore.getState().eventInstance;
      if (!currentEventInstance) return;
      useEventStore.getState().unregisterEventListener(listenerId);
    },
    [], // 의존성 제거
  );

  /**
   * 모든 리스너 정리 - 의존성 최소화
   */
  const cleanupAllListeners = useCallback(() => {
    const currentEventInstance = useEventStore.getState().eventInstance;
    if (!currentEventInstance) return;
    useEventStore.getState().cleanupAllEventListeners();
  }, []); // 의존성 제거

  // 🆕 전역 컨텍스트 메뉴 리스너 - 단순화된 초기화
  const initializeGlobalContextMenu = useCallback(() => {
    // 이미 등록되어 있으면 건너뛰기
    if (globalThis.__globalContextMenuCleanup) {
      return;
    }

    const currentMap = map;
    const currentEventInstance = useEventStore.getState().eventInstance;

    if (!currentMap || !currentEventInstance) {
      return;
    }

    // console.log("🎯 전역 컨텍스트 메뉴 리스너 등록 중...");

    const cleanup = registerListener(
      currentMap,
      "contextmenu",
      (event: any) => {
        // 브라우저 기본 컨텍스트 메뉴 방지
        event?.originalEvent?.preventDefault?.();
        event?.originalEvent?.stopPropagation?.();

        // console.log("🎯 전역 컨텍스트 메뉴 이벤트 감지:", event);

        const queue = layerQueue();
        if (queue.size === 0) return;

        // 클릭 위치에서 모든 feature 조회
        const allFeatures =
          currentMap.selectFeatureOnClick?.({ pixel: event.pixel }) || [];

        // 우선순위 순으로 정렬된 레이어 큐
        const sortedLayers = Array.from(queue.values()).sort(
          (a, b) => b.priority - a.priority,
        );

        // 큐를 순회하며 첫 번째 매칭되는 레이어 찾기
        for (const queueItem of sortedLayers) {
          const layerId = queueItem.layer.getODFId?.();
          if (!layerId) continue;

          const matchedFeatures = allFeatures.filter(
            (obj: any) => obj.layer?.getODFId?.() === layerId,
          );

          if (matchedFeatures.length > 0) {
            const feature = matchedFeatures[0].feature;

            const layerFeatureInfo: LayerFeatureInfo = {
              layer: queueItem.layer,
              layerId: queueItem.layerId,
              layerType: queueItem.layerType,
              feature,
              featureId: feature.getId?.() || `feature_${Date.now()}`,
              featureType: feature.getGeometry?.()?.getType?.() || "unknown",
              coordinates: feature.getGeometry?.()?.getCoordinates?.() || [],
              properties: feature.getProperties?.() || {},
            };

            const result: LayerDetectionResult = {
              layerFeature: layerFeatureInfo,
              pixel: event.pixel,
              position: event.coordinate,
              allFeatures,
            };

            // 등록된 콜백들에게 결과 전달
            const callbacks = globalThis.__layerDetectionCallbacks || [];
            callbacks.forEach((callback: any) => callback(result));
            return;
          }
        }

        // 매칭되는 feature가 없는 경우
        const result: LayerDetectionResult = {
          layerFeature: null,
          pixel: event.pixel,
          position: event.coordinate,
          allFeatures,
        };

        const callbacks = globalThis.__layerDetectionCallbacks || [];
        callbacks.forEach((callback: any) => callback(result));
      },
      { listenerId: "global_contextmenu", once: false },
    );

    // console.log("🎯 registerListener 결과:", !!cleanup);
    globalThis.__globalContextMenuCleanup = cleanup;
  }, [map, registerListener]); // map과 registerListener 의존성 추가

  // 🆕 레이어 큐에 레이어 추가 - 단순화
  const addLayerToQueue = useCallback(
    (layer: any, layerType: string, priority: number = 0) => {
      const queue = layerQueue();
      const layerId = layer.getODFId?.() || `layer_${Date.now()}`;

      // console.log("🎯 addLayerToQueue 호출됨:", {
      //   layerId,
      //   layerType,
      //   priority,
      // });

      queue.set(layerId, {
        layer,
        layerId,
        layerType,
        priority,
      });

      // console.log("🎯 현재 큐 상태:", Array.from(queue.entries()));

      // 전역 컨텍스트 메뉴 리스너 초기화 (한 번만)
      initializeGlobalContextMenu();
    },
    [initializeGlobalContextMenu], // initializeGlobalContextMenu 의존성 추가
  );

  // 🆕 레이어 큐에서 레이어 제거 - 단순화
  const removeLayerFromQueue = useCallback(
    (layerId: string) => {
      const queue = layerQueue();
      queue.delete(layerId);
    },
    [], // 의존성 제거
  );

  // 🆕 레이어 감지 콜백 등록
  const registerLayerDetection = useCallback(
    (callback: (result: LayerDetectionResult) => void) => {
      if (!globalThis.__layerDetectionCallbacks) {
        globalThis.__layerDetectionCallbacks = [];
      }

      globalThis.__layerDetectionCallbacks.push(callback);

      // 정리 함수 반환
      return () => {
        if (globalThis.__layerDetectionCallbacks) {
          const index = globalThis.__layerDetectionCallbacks.indexOf(callback);
          if (index > -1) {
            globalThis.__layerDetectionCallbacks.splice(index, 1);
          }
        }
      };
    },
    [],
  );

  // 🆕 고수준 레이어 컨텍스트 메뉴 등록
  const registerLayerContextMenu = useCallback(
    (
      layers: Array<{ layer: any; layerType: string; priority?: number }>,
      onLayerDetected: (result: LayerDetectionResult) => void,
    ) => {
      // 레이어들을 큐에 추가
      layers.forEach(({ layer, layerType, priority = 0 }) => {
        addLayerToQueue(layer, layerType, priority);
      });

      // 감지 콜백 등록
      const cleanup = registerLayerDetection(onLayerDetected);

      // 정리 함수 반환 (레이어 큐에서 제거 + 콜백 제거)
      return () => {
        layers.forEach(({ layer }) => {
          const layerId = layer.getODFId?.() || "";
          if (layerId) {
            removeLayerFromQueue(layerId);
          }
        });
        cleanup();
      };
    },
    [addLayerToQueue, registerLayerDetection, removeLayerFromQueue], // 의존성 추가
  );

  // 컴포넌트 언마운트 시 자동 정리 - 의존성 제거
  useEffect(() => {
    return () => {
      // console.log("useEvent 훅 언마운트, 모든 이벤트 리스너 정리 중...");
      // 전역 컨텍스트 메뉴 정리
      if (globalThis.__globalContextMenuCleanup) {
        try {
          globalThis.__globalContextMenuCleanup();
        } catch (e) {
          console.error("전역 컨텍스트 메뉴 정리 실패:", e);
        }
        globalThis.__globalContextMenuCleanup = undefined;
      }
      // cleanupAllListeners(); // 필요시 활성화
    };
  }, []); // 의존성 완전 제거

  return {
    registerListener,
    unregisterListener,
    cleanupAllListeners,
    isReady: !!eventInstance,
    // Store 상태 접근 - 실시간 조회
    get listenerCount() {
      return useEventStore.getState().activeListeners.size;
    },
    get hasErrors() {
      return useEventStore.getState().eventErrors.size > 0;
    },

    // 🆕 레이어 기반 컨텍스트 메뉴 API
    addLayerToQueue,
    removeLayerFromQueue,
    registerLayerDetection,
    registerLayerContextMenu,
  };
}
