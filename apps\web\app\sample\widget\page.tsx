"use client";
import { Map, useMap, useMapActions } from "@geon-map/react-odf";
import {
  DownloadWidget,
  MouseCoordWidget,
  OverviewWidget,
  ScaleWidget,
  SpatialSearchWidget,
} from "@geon-map/react-ui/components";
import { RoadviewWidget } from "@geon-map/react-ui/components";
import { useEffect, useRef, useState } from "react";

import AddressSearchWidgetPackage from "@/components/widget/address-search-widget-pakage";
import BasemapWidgetUse from "@/components/widget/basemap-widget-use";
import DrawWidgetPakage from "@/components/widget/draw-widget-pakage";
import EstateWidgetPackage from "@/components/widget/estate-widget-package";
import LayerFileDownloadPackage from "@/components/widget/layer-file-download-widget-pakage";
import MoveMapHomeWidgetPackage from "@/components/widget/move-map-home-widget-package";
import RegionSelectorWidgetPackage from "@/components/widget/region-selector-widget-pakage";

export default function Page() {
  return (
    <Map className="h-[800px] w-full">
      {/* 🎯 상태 동기화 테스트 모니터 */}
      <MapStateMonitor />

      <BasemapWidgetUse />
      <AddressSearchWidgetPackage />
      <DrawWidgetPakage />
      {/* <MouseCoordDisplay /> */}
      <MouseCoordWidget />
      <RoadviewWidget />
      <OverviewWidget />
      <RegionSelectorWidgetPackage />
      <ScaleWidget />
      <DownloadWidget />
      <SpatialSearchWidget targetLayerId="muan_gis:2017_2018_n1a_h0010000" />
      {/* LayerFileDownloadPackage 의 layerFileDownloadInfo 는 임시데이터, buttonName 은 버튼 이름 지정 */}
      <LayerFileDownloadPackage
        buttonName="레이어 다운로드"
        // dxf 는 아직 API 작동 X
        activeDownloadFormats={["csv", "shape", "geojson", "kml", "kmz", "dxf"]}
        layerFileDownloadInfo={{
          // trgetTypeName: "Wgeonapi:L100003391",
          trgetTypeName: "muan_gis:2017_2018_n1a_a0010000",
        }}
      />
      <EstateWidgetPackage />
      <MoveMapHomeWidgetPackage zoom={5} center={[906733.5, 1666654.4]} />
    </Map>
  );
}
// 지도 상태 동기화 테스트 컴포넌트
function MapStateMonitor() {
  const { center, zoom, scale, isLoading } = useMap();
  const { setCenter, setZoom } = useMapActions();
  const [updateCount, setUpdateCount] = useState(0);

  const throttleRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (throttleRef.current) {
      clearTimeout(throttleRef.current);
    }

    // 쓰로틀링
    throttleRef.current = setTimeout(() => {
      setUpdateCount((prev) => prev + 1);
    }, 100);

    return () => {
      if (throttleRef.current) {
        clearTimeout(throttleRef.current);
      }
    };
  }, [center, zoom, scale]);

  return (
    <div className="absolute left-4 top-4 z-50 max-w-[280px] rounded-lg bg-white/90 p-3 text-xs shadow-lg backdrop-blur">
      <h4 className="mb-2 text-sm font-bold text-blue-600">
        🔄 상태 동기화 테스트
      </h4>

      <div className="space-y-1">
        <div className="flex justify-between">
          <span>상태:</span>
          <span className={isLoading ? "text-green-600" : "text-orange-600"}>
            {isLoading ? "로딩중" : "준비됨"}
          </span>
        </div>

        <div className="flex justify-between">
          <span>중심점:</span>
          <span className="font-mono">
            [{center[0].toFixed(1)}, {center[1].toFixed(1)}]
          </span>
        </div>

        <div className="flex justify-between">
          <span>줌:</span>
          <span>{zoom}</span>
        </div>

        <div className="flex justify-between">
          <span>축척:</span>
          <span>{scale || "계산중"}</span>
        </div>

        <div className="flex justify-between border-t pt-1">
          <span>업데이트:</span>
          <span className="font-bold text-blue-600">{updateCount}회</span>
        </div>
      </div>

      {/* 빠른 테스트 버튼들 */}
      <div className="mt-2 space-y-1 border-t pt-2">
        <div className="flex gap-1">
          <button
            onClick={() => setCenter([126.978, 37.5665], "4326")}
            className="rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
          >
            서울
          </button>
          <button
            onClick={() => setCenter([129.0756, 35.1796], "4326")}
            className="rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
          >
            부산
          </button>
        </div>
        <div className="flex gap-1">
          <button
            onClick={() => setZoom(8)}
            className="rounded bg-green-500 px-2 py-1 text-xs text-white hover:bg-green-600"
          >
            줌8
          </button>
          <button
            onClick={() => setZoom(15)}
            className="rounded bg-green-500 px-2 py-1 text-xs text-white hover:bg-green-600"
          >
            줌15
          </button>
        </div>
      </div>
    </div>
  );
}
