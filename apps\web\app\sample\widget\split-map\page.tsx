"use client";

import { Map, useMap } from "@geon-map/react-odf";
import { useState, useMemo } from "react";

import BasemapWidgetUse from "@/components/widget/basemap-widget-use";

/**
 * 🎯 지도 디버그 정보 표시 컴포넌트
 */
function MapDebugInfo({
  position = "top-left",
}: {
  position?: "top-left" | "top-right";
}) {
  const { center, zoom } = useMap();

  const positionClasses = {
    "top-left": "top-2 left-2",
    "top-right": "top-2 right-2",
  };

  return (
    <div
      className={`absolute ${positionClasses[position]} z-50 max-w-[200px] rounded-lg bg-black/80 p-2 text-xs text-white`}
    >
      <div className="space-y-1">
        <div>
          Center: [{center[0].toFixed(0)}, {center[1].toFixed(0)}]
        </div>
        <div>Zoom: {zoom}</div>
      </div>
    </div>
  );
}

/**
 * 🎯 분할 지도 페이지
 */
export default function SplitMapPage() {
  const [splitCount, setSplitCount] = useState<1 | 2 | 3 | 4>(1);

  return (
    <div className="flex h-screen flex-col">
      {/* 헤더 - 분할 모드 제어 */}
      <div className="flex items-center justify-between bg-gray-800 p-4 text-white">
        <h1 className="text-xl font-bold">분할 지도 테스트</h1>

        {/* 분할 모드 스위치 */}
        <div className="flex items-center gap-4">
          <span className="text-sm">분할 모드:</span>
          <div className="flex rounded-lg bg-gray-700 p-1">
            {[1, 2, 3, 4].map((count) => (
              <button
                key={count}
                onClick={() => setSplitCount(count as 1 | 2 | 3 | 4)}
                className={`rounded-md px-4 py-2 transition-colors ${
                  splitCount === count
                    ? "bg-blue-600 text-white"
                    : "text-gray-300 hover:bg-gray-600"
                }`}
              >
                {count}분할
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="relative flex-1">
        <Map className="h-full w-full" splitMode={{ count: splitCount }}>
          <MapDebugInfo position="top-left" />
          <BasemapWidgetUse />
        </Map>
      </div>
    </div>
  );
}
