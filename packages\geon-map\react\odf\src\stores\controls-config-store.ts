/**
 * 🎯 Controls Config 전역 Store
 *
 * React Context 외부에서도 Controls 설정에 접근할 수 있도록
 * 전역 상태로 관리하는 Store입니다.
 */

import { create } from "zustand";

import type { ControlsConfig } from "../providers/controls-provider";

interface ControlsConfigState {
  config: ControlsConfig | null;
  setConfig: (config: ControlsConfig | null) => void;
  clearConfig: () => void;
}

/**
 * Controls Config 전역 Store
 */
export const useControlsConfigStore = create<ControlsConfigState>((set) => ({
  config: null,
  setConfig: (config) => set({ config }),
  clearConfig: () => set({ config: null }),
}));

/**
 * React Context 외부에서 Controls Config에 접근하는 헬퍼 함수
 */
export function getControlsConfig(): ControlsConfig | null {
  return useControlsConfigStore.getState().config;
}

/**
 * Controls Config를 전역 Store에 저장하는 헬퍼 함수
 */
export function setGlobalControlsConfig(config: ControlsConfig | null): void {
  useControlsConfigStore.getState().setConfig(config);
}
