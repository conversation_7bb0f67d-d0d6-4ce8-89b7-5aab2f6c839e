{"name": "web", "version": "0.1.2", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "dev:prod": "cross-env NODE_ENV=production next dev --turbopack --port 3001", "analyze": "cross-env ANALYZE=true next build", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@config/tailwind": "workspace:^", "@geon-map/core": "workspace:^", "@geon-map/react-odf": "workspace:^", "@geon-map/react-ui": "workspace:^", "@geon-query/model": "workspace:^", "@geon-query/react-query": "workspace:*", "@geon-ui/react": "workspace:^", "@svgr/webpack": "^8.1.0", "lucide-react": "^0.542.0", "next": "^15.5.2", "react": "^19.1.1", "react-dom": "^19.1.1", "zustand": "^5.0.8"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@next/bundle-analyzer": "^15.5.2", "@storybook/addon-docs": "^9.1.3", "@storybook/addon-links": "^9.1.3", "@storybook/nextjs": "^9.1.3", "@tailwindcss/postcss": "^4.1.12", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^22.18.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "autoprefixer": "^10.4.21", "eslint": "^9.34.0", "eslint-plugin-storybook": "^9.1.3", "jest": "^30.1.2", "jest-environment-jsdom": "^30.1.2", "postcss-loader": "^8.1.1", "prettier-plugin-tailwindcss": "^0.6.14", "raw-loader": "^4.0.2", "storybook": "^9.1.3", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}