"use client";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@geon-ui/react/primitives/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@geon-ui/react/primitives/sidebar";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

import { useMapSidebar } from "../../_contexts/sidebar";
import { iconMap, SERVICES } from "../../_utils";

export default function NavService() {
  const { active, setActive, outer, innerOpen, setInnerOpen, setContent } =
    useMapSidebar();

  return (
    <SidebarGroup>
      <SidebarMenu>
        {SERVICES.map((item) => {
          const Icon = iconMap[item.icon];

          return item.items ? (
            <Collapsible
              key={item.id}
              asChild
              className="group/collapsible"
              open={active === item.id}
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    tooltip={item.title}
                    onClick={() => {
                      if (!outer.open) outer.setOpen(true);
                      if (innerOpen) setInnerOpen(false);
                      setActive(item.id);
                    }}
                  >
                    <Icon />
                    <span>{item.title}</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.id}>
                        <SidebarMenuSubButton
                          asChild
                          onClick={() => {
                            setInnerOpen(true);
                            outer.setOpen(false);
                            setContent(
                              subItem.content ||
                                `${item.title} > ${subItem.title}`,
                            );
                          }}
                        >
                          <Link href={`/service/${item.id}/${subItem.id}`}>
                            {subItem.title}
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          ) : (
            <SidebarMenuItem key={item.id}>
              <SidebarMenuButton asChild tooltip={item.title}>
                <Link
                  href={`/service/${item.id}`}
                  onClick={() => {
                    setInnerOpen(true);
                    outer.setOpen(false);
                    setContent(item.title);
                  }}
                >
                  <Icon />
                  {item.title}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
