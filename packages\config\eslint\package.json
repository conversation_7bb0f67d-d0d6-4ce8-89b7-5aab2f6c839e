{"name": "@config/eslint", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": {"default": "./base.js"}, "./next-js": {"default": "./next-js.js"}, "./react-internal": {"default": "./react-internal.js"}}, "devDependencies": {"@config/tailwind": "workspace:^", "@eslint/js": "^9.34.0", "@next/eslint-plugin-next": "^15.5.2", "@typescript-eslint/parser": "^8.41.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-turbo": "^2.5.6", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^16.3.0", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0"}}